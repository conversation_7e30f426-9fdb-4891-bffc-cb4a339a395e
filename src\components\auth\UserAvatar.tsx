import { useAuth } from "@/contexts/AuthContext";
import { useState, useRef, useEffect } from "react";
import { Settings, LogOut } from "lucide-react";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";

export const UserAvatar = () => {
  const { user, signOut } = useAuth();
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<'right' | 'left'>('right');

  if (!user) {
    return null;
  }

  const handleSignOut = async () => {
    await signOut();
    setShowMenu(false);
  };

  const handleManageAccount = () => {
    // Navigate to profile page
    window.location.href = "/profile";
    setShowMenu(false);
  };

  // Determine the menu position based on the element's position on the screen
  useEffect(() => {
    if (showMenu && menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect();
      const windowWidth = window.innerWidth;

      // If the menu will be outside the right side, display it from the left side
      if (rect.right > windowWidth - 20) {
        setMenuPosition('left');
      } else {
        setMenuPosition('right');
      }
    }
  }, [showMenu]);

  const userName = getUserDisplayName(user);
  const userEmail = user.email || '';
  const userAvatar = getUserAvatar(user);

  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  return (
    <div className="relative">
      {/* Enhanced User Avatar */}
      <div
        className="w-10 h-10 rounded-full border-2 border-pegasus-orange/50 hover:border-pegasus-orange transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl hover:shadow-pegasus-orange/30 overflow-hidden transform hover:scale-105 active:scale-95 ring-2 ring-transparent hover:ring-pegasus-orange/20"
        onClick={toggleMenu}
        title={`Account Management - ${userName}`}
      >
        {userAvatar ? (
          <img
            src={userAvatar}
            alt="User Avatar"
            className="w-full h-full object-cover rounded-full transition-all duration-300 hover:brightness-110"
            onError={(e) => {
              // In case of image loading failure, display the first letter
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-pegasus-orange via-orange-500 to-pegasus-blue flex items-center justify-center text-white font-bold text-sm shadow-inner">
            {userName.charAt(0).toUpperCase()}
          </div>
        )}
      </div>

      {/* Dropdown Menu */}
      {showMenu && (
        <>
          {/* Transparent background for closing the menu */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowMenu(false)}
          />

          {/* Enhanced Menu */}
          <div
            ref={menuRef}
            className={`absolute top-full mt-3 w-72 bg-gradient-to-br from-gray-900/98 to-gray-800/95 backdrop-blur-md border border-gray-700/50 shadow-2xl rounded-xl z-50 overflow-hidden ${
              menuPosition === 'right' ? 'right-0' : 'left-0'
            }`}
          >
            {/* Enhanced User Information */}
            <div className="p-5 border-b border-gray-700/60 bg-gradient-to-r from-gray-800/50 to-gray-700/30">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-pegasus-orange/40 shadow-lg ring-2 ring-pegasus-orange/20">
                  {userAvatar ? (
                    <img
                      src={userAvatar}
                      alt="User Avatar"
                      className="w-full h-full object-cover transition-all duration-300 hover:scale-110"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-pegasus-orange via-orange-500 to-pegasus-blue flex items-center justify-center text-white font-bold text-base shadow-inner">
                      {userName.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-gray-100 font-bold text-base truncate">
                    {userName}
                  </div>
                  <div className="text-gray-400/90 text-sm truncate font-medium">
                    {userEmail}
                  </div>
                  <div className="text-pegasus-orange/80 text-xs font-medium mt-1">
                    Account Active
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Menu Options */}
            <div className="p-3 space-y-2">
              <button
                onClick={handleManageAccount}
                className="w-full text-left text-gray-200 hover:bg-gray-800/60 hover:text-white transition-all duration-300 rounded-lg p-4 flex items-center gap-4 group transform hover:scale-[1.02] active:scale-[0.98] shadow-sm hover:shadow-md"
              >
                <div className="w-10 h-10 rounded-lg bg-pegasus-orange/20 flex items-center justify-center group-hover:bg-pegasus-orange/30 transition-all duration-300">
                  <Settings className="text-pegasus-orange w-5 h-5 flex-shrink-0 group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <div className="flex-1">
                  <span className="text-gray-200 font-semibold text-sm block">Manage Account</span>
                  <span className="text-gray-400/80 text-xs">Profile settings & preferences</span>
                </div>
              </button>

              <button
                onClick={handleSignOut}
                className="w-full text-left text-gray-200 hover:bg-red-500/15 hover:text-red-300 transition-all duration-300 rounded-lg p-4 flex items-center gap-4 group transform hover:scale-[1.02] active:scale-[0.98] shadow-sm hover:shadow-md"
              >
                <div className="w-10 h-10 rounded-lg bg-red-500/20 flex items-center justify-center group-hover:bg-red-500/30 transition-all duration-300">
                  <LogOut className="text-red-400 w-5 h-5 flex-shrink-0 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
                <div className="flex-1">
                  <span className="font-semibold text-sm block">Sign Out</span>
                  <span className="text-gray-400/80 text-xs">End your current session</span>
                </div>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserAvatar;
