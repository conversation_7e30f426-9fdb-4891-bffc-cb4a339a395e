import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "./ProtectedRoute";
import { motion, AnimatePresence } from "framer-motion";
import { Settings, Shield, User, Bell, Key, Palette, ArrowLeft, Save, Edit3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link } from "react-router-dom";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { toast } from "sonner";

export const NewUserProfilePage = () => {
  const { user, updateProfile, loading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
  });

  // Initialize form data when user is loaded
  useState(() => {
    if (user) {
      setFormData({
        full_name: getUserDisplayName(user),
        email: user.email || "",
      });
    }
  });

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const { error } = await updateProfile({
        data: {
          full_name: formData.full_name,
        }
      });

      if (!error) {
        setIsEditing(false);
        toast.success("Profile updated successfully!");
      }
    } catch (err) {
      console.error("Error updating profile:", err);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  if (!user) return null;

  const userName = getUserDisplayName(user);
  const userAvatar = getUserAvatar(user);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto space-y-8"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-300"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <h1 className="text-3xl font-bold text-white">Profile Settings</h1>
            </div>
          </motion.div>

          {/* Profile Card */}
          <motion.div variants={itemVariants}>
            <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-6 mb-6">
                  <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-purple-500/30">
                    {userAvatar ? (
                      <img
                        src={userAvatar}
                        alt="User Avatar"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-2xl">
                        {userName.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-white mb-1">{userName}</h2>
                    <p className="text-gray-400">{user.email}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-green-400 text-sm">Account Active</span>
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsEditing(!isEditing)}
                    variant="outline"
                    className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                  >
                    <Edit3 className="w-4 h-4 mr-2" />
                    {isEditing ? "Cancel" : "Edit Profile"}
                  </Button>
                </div>

                <AnimatePresence>
                  {isEditing && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-4 border-t border-gray-700 pt-6"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="full_name" className="text-gray-300">
                            Full Name
                          </Label>
                          <Input
                            id="full_name"
                            value={formData.full_name}
                            onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                            className="bg-gray-700 border-gray-600 text-white"
                            placeholder="Enter your full name"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-gray-300">
                            Email
                          </Label>
                          <Input
                            id="email"
                            value={formData.email}
                            disabled
                            className="bg-gray-700 border-gray-600 text-gray-400"
                            placeholder="Email cannot be changed"
                          />
                        </div>
                      </div>
                      <div className="flex gap-3">
                        <Button
                          onClick={handleSave}
                          disabled={isLoading}
                          className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {isLoading ? "Saving..." : "Save Changes"}
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Actions */}
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-semibold text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                {
                  icon: Shield,
                  title: "Security",
                  desc: "Manage your account security",
                  color: "text-green-400",
                  bgColor: "bg-green-500/10",
                  action: () => toast.info("Security settings coming soon!")
                },
                {
                  icon: Bell,
                  title: "Notifications",
                  desc: "Configure notification preferences",
                  color: "text-blue-400",
                  bgColor: "bg-blue-500/10",
                  action: () => toast.info("Notification settings coming soon!")
                },
                {
                  icon: Key,
                  title: "API Keys",
                  desc: "Manage your API access",
                  color: "text-purple-400",
                  bgColor: "bg-purple-500/10",
                  action: () => toast.info("API management coming soon!")
                }
              ].map((action, index) => (
                <Card
                  key={index}
                  className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-all duration-300 cursor-pointer"
                  onClick={action.action}
                >
                  <CardContent className="p-4">
                    <div className={`w-12 h-12 rounded-lg ${action.bgColor} flex items-center justify-center mb-3`}>
                      <action.icon className={`w-6 h-6 ${action.color}`} />
                    </div>
                    <h4 className="text-white font-semibold mb-1">{action.title}</h4>
                    <p className="text-gray-400 text-sm">{action.desc}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </ProtectedRoute>
  );
};

export default NewUserProfilePage;
