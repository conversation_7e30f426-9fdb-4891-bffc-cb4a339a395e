import { useAuth } from "@/contexts/AuthContext";
import { useState, useRef, useEffect } from "react";
import { Settings, LogOut, User } from "lucide-react";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";

export const UserAvatar = () => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<'right' | 'left'>('right');

  // Function to get theme colors based on current page (matching Navbar)
  const getThemeColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      // Home page - Purple theme
      return {
        primary: '#C084FC',
        primaryRgb: '192, 132, 252',
        background: 'from-purple-900/95 to-purple-800/90',
        border: 'border-purple-500/30',
        accent: 'bg-purple-500/20',
        accentHover: 'group-hover:bg-purple-500/30',
        text: 'text-purple-400',
        glow: 'shadow-purple-500/20',
        ring: 'ring-purple-500/20'
      };
    } else if (currentPath === '/software') {
      // Software page - Orange theme
      return {
        primary: '#F97316',
        primaryRgb: '249, 115, 22',
        background: 'from-orange-900/95 to-orange-800/90',
        border: 'border-orange-500/30',
        accent: 'bg-orange-500/20',
        accentHover: 'group-hover:bg-orange-500/30',
        text: 'text-orange-400',
        glow: 'shadow-orange-500/20',
        ring: 'ring-orange-500/20'
      };
    } else if (currentPath === '/hardware') {
      // Hardware page - Blue theme
      return {
        primary: '#3B82F6',
        primaryRgb: '59, 130, 246',
        background: 'from-blue-900/95 to-blue-800/90',
        border: 'border-blue-500/30',
        accent: 'bg-blue-500/20',
        accentHover: 'group-hover:bg-blue-500/30',
        text: 'text-blue-400',
        glow: 'shadow-blue-500/20',
        ring: 'ring-blue-500/20'
      };
    } else {
      // Default - Purple theme
      return {
        primary: '#C084FC',
        primaryRgb: '192, 132, 252',
        background: 'from-purple-900/95 to-purple-800/90',
        border: 'border-purple-500/30',
        accent: 'bg-purple-500/20',
        accentHover: 'group-hover:bg-purple-500/30',
        text: 'text-purple-400',
        glow: 'shadow-purple-500/20',
        ring: 'ring-purple-500/20'
      };
    }
  };

  const themeColors = getThemeColors();

  if (!user) {
    return null;
  }

  const handleSignOut = async () => {
    await signOut();
    setShowMenu(false);
  };

  const handleManageAccount = () => {
    // Navigate to profile page
    window.location.href = "/profile";
    setShowMenu(false);
  };

  // Determine the menu position based on the element's position on the screen
  useEffect(() => {
    if (showMenu && menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect();
      const windowWidth = window.innerWidth;

      // If the menu will be outside the right side, display it from the left side
      if (rect.right > windowWidth - 20) {
        setMenuPosition('left');
      } else {
        setMenuPosition('right');
      }
    }
  }, [showMenu]);

  const userName = getUserDisplayName(user);
  const userEmail = user.email || '';
  const userAvatar = getUserAvatar(user);

  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  return (
    <div className="relative">
      {/* Enhanced User Avatar with Dynamic Theme */}
      <motion.div
        className={`w-10 h-10 rounded-full border-2 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden ring-2 ring-transparent ${themeColors.border} hover:shadow-xl ${themeColors.glow} hover:scale-105 active:scale-95 hover:${themeColors.ring}`}
        onClick={toggleMenu}
        title={`Account Management - ${userName}`}
        whileHover={{
          scale: 1.05,
          boxShadow: `0 0 20px rgba(${themeColors.primaryRgb}, 0.4)`
        }}
        whileTap={{ scale: 0.95 }}
        style={{
          borderColor: `rgba(${themeColors.primaryRgb}, 0.5)`
        }}
      >
        {userAvatar ? (
          <img
            src={userAvatar}
            alt="User Avatar"
            className="w-full h-full object-cover rounded-full transition-all duration-300 hover:brightness-110"
            onError={(e) => {
              // In case of image loading failure, display the first letter
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <div
            className="w-full h-full flex items-center justify-center text-white font-bold text-sm shadow-inner"
            style={{
              background: `linear-gradient(135deg, rgba(${themeColors.primaryRgb}, 0.8) 0%, rgba(${themeColors.primaryRgb}, 0.6) 100%)`
            }}
          >
            {userName.charAt(0).toUpperCase()}
          </div>
        )}
      </motion.div>

      {/* Enhanced Dropdown Menu with Dynamic Theme */}
      <AnimatePresence>
        {showMenu && (
          <>
            {/* Transparent background for closing the menu */}
            <motion.div
              className="fixed inset-0 z-40"
              onClick={() => setShowMenu(false)}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />

            {/* Enhanced Menu with Theme Colors */}
            <motion.div
              ref={menuRef}
              className={`absolute top-full mt-3 w-80 backdrop-blur-xl border shadow-2xl rounded-2xl z-50 overflow-hidden ${
                menuPosition === 'right' ? 'right-0' : 'left-0'
              } ${themeColors.border}`}
              style={{
                background: `linear-gradient(135deg, rgba(${themeColors.primaryRgb}, 0.1) 0%, rgba(0, 0, 0, 0.95) 100%)`,
                backdropFilter: 'blur(20px)',
                boxShadow: `0 25px 50px -12px rgba(${themeColors.primaryRgb}, 0.25)`
              }}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              {/* Enhanced User Information with Theme */}
              <div
                className="p-6 border-b border-opacity-30"
                style={{
                  borderColor: `rgba(${themeColors.primaryRgb}, 0.3)`,
                  background: `linear-gradient(135deg, rgba(${themeColors.primaryRgb}, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%)`
                }}
              >
                <div className="flex items-center gap-4">
                  <motion.div
                    className="w-14 h-14 rounded-full overflow-hidden border-2 shadow-lg ring-2"
                    style={{
                      borderColor: `rgba(${themeColors.primaryRgb}, 0.6)`,
                      boxShadow: `0 0 20px rgba(${themeColors.primaryRgb}, 0.3)`
                    }}
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {userAvatar ? (
                      <img
                        src={userAvatar}
                        alt="User Avatar"
                        className="w-full h-full object-cover transition-all duration-300"
                      />
                    ) : (
                      <div
                        className="w-full h-full flex items-center justify-center text-white font-bold text-lg shadow-inner"
                        style={{
                          background: `linear-gradient(135deg, rgba(${themeColors.primaryRgb}, 0.8) 0%, rgba(${themeColors.primaryRgb}, 0.6) 100%)`
                        }}
                      >
                        {userName.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </motion.div>
                  <div className="flex-1 min-w-0">
                    <div className="text-white font-bold text-lg truncate">
                      {userName}
                    </div>
                    <div className="text-gray-300/90 text-sm truncate font-medium">
                      {userEmail}
                    </div>
                    <div
                      className="text-xs font-semibold mt-2 px-2 py-1 rounded-full inline-block"
                      style={{
                        color: themeColors.primary,
                        backgroundColor: `rgba(${themeColors.primaryRgb}, 0.2)`
                      }}
                    >
                      ● Account Active
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Menu Options with Theme */}
              <div className="p-4 space-y-3">
                <motion.button
                  onClick={handleManageAccount}
                  className="w-full text-left text-gray-200 hover:text-white transition-all duration-300 rounded-xl p-4 flex items-center gap-4 group transform hover:scale-[1.02] active:scale-[0.98] shadow-sm hover:shadow-lg"
                  style={{
                    background: `rgba(${themeColors.primaryRgb}, 0.05)`,
                  }}
                  whileHover={{
                    background: `rgba(${themeColors.primaryRgb}, 0.15)`,
                    boxShadow: `0 8px 25px rgba(${themeColors.primaryRgb}, 0.2)`
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.div
                    className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300"
                    style={{
                      backgroundColor: `rgba(${themeColors.primaryRgb}, 0.2)`
                    }}
                    whileHover={{
                      backgroundColor: `rgba(${themeColors.primaryRgb}, 0.3)`,
                      rotate: 90
                    }}
                  >
                    <Settings
                      className="w-5 h-5 flex-shrink-0 transition-transform duration-300"
                      style={{ color: themeColors.primary }}
                    />
                  </motion.div>
                  <div className="flex-1">
                    <span className="text-white font-semibold text-sm block">Manage Account</span>
                    <span className="text-gray-300/80 text-xs">Update your profile settings</span>
                  </div>
                </motion.button>

                <motion.button
                  onClick={handleSignOut}
                  className="w-full text-left text-gray-200 hover:text-red-300 transition-all duration-300 rounded-xl p-4 flex items-center gap-4 group transform hover:scale-[1.02] active:scale-[0.98] shadow-sm hover:shadow-lg"
                  style={{
                    background: `rgba(239, 68, 68, 0.05)`,
                  }}
                  whileHover={{
                    background: `rgba(239, 68, 68, 0.15)`,
                    boxShadow: `0 8px 25px rgba(239, 68, 68, 0.2)`
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.div
                    className="w-12 h-12 rounded-xl bg-red-500/20 flex items-center justify-center transition-all duration-300"
                    whileHover={{
                      backgroundColor: `rgba(239, 68, 68, 0.3)`,
                      x: 2
                    }}
                  >
                    <LogOut className="text-red-400 w-5 h-5 flex-shrink-0 transition-transform duration-300" />
                  </motion.div>
                  <div className="flex-1">
                    <span className="font-semibold text-sm block text-white">Sign Out</span>
                    <span className="text-gray-300/80 text-xs">End your current session</span>
                  </div>
                </motion.button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserAvatar;
